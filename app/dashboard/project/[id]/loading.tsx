import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    // Mimic the root div of ProjectPageContent
    <div className="flex flex-col h-full w-full p-0">
      {/* Mimic the inner div that organizes header and main content */}
      <div className="flex flex-col h-full gap-0">
        {/* Skeleton for ProjectHeader (flex-none) */}
        <div className="flex-none p-4 border-b">
          <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            {/* Left side: Project Name, Meta */}
            <div className="space-y-2">
              <Skeleton className="h-7 w-48 md:w-64" /> {/* Project Name */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <Skeleton className="h-4 w-24" /> {/* Created At / Status */}
                <Skeleton className="h-4 w-24" /> {/* Updated At */}
              </div>
            </div>
            {/* Right side: Actions / Buttons placeholder */}
            <div className="flex items-center gap-2 mt-2 md:mt-0">
              <Skeleton className="h-9 w-24" />
              <Skeleton className="h-9 w-9" />
            </div>
          </div>
          {/* Accordion Trigger Skeleton (Simplified) */}
          <div className="mt-4">
            <Skeleton className="h-6 w-1/3" />
          </div>
        </div>

        {/* Skeleton for Main Content Area (flex-1 overflow-y-auto min-h-0) */}
        <div className="flex-1 overflow-y-auto min-h-0 p-6">
          <div className="space-y-6">
            {/* Example: Skeleton for a step title and form elements */}
            <Skeleton className="h-8 w-1/2" />
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
              <div className="flex justify-end">
                <Skeleton className="h-10 w-28" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}