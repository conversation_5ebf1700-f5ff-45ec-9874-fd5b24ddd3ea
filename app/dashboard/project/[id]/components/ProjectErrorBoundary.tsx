"use client";

import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface ProjectErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

function ProjectErrorFallback({ error, resetErrorBoundary }: ProjectErrorFallbackProps) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-4 text-center max-w-2xl mx-auto">
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-5 w-5" />
        <AlertTitle className="text-lg font-semibold">Project Loading Error</AlertTitle>
        <AlertDescription className="mt-2">
          {error.message || "An unexpected error occurred while loading the project."}
          {isDevelopment && (
            <details className="mt-4 text-left">
              <summary className="cursor-pointer font-medium">Technical Details (Development)</summary>
              <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                {error.stack}
              </pre>
            </details>
          )}
        </AlertDescription>
      </Alert>
      
      <div className="flex gap-3">
        <Button 
          onClick={resetErrorBoundary}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
        
        <Button 
          variant="outline"
          onClick={() => {
            // Clear any cached data and reload
            if (typeof window !== 'undefined') {
              localStorage.removeItem('project-cache');
              sessionStorage.clear();
              window.location.href = '/dashboard';
            }
          }}
        >
          Go to Dashboard
        </Button>
      </div>
      
      <p className="text-sm text-muted-foreground mt-4">
        If this problem persists, please try refreshing the page or contact support.
      </p>
    </div>
  );
}

interface ProjectErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export function ProjectErrorBoundary({ children, onError }: ProjectErrorBoundaryProps) {
  return (
    <ErrorBoundary
      FallbackComponent={ProjectErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Project Error Boundary caught an error:', error, errorInfo);
        
        // Log to external service in production
        if (process.env.NODE_ENV === 'production') {
          // You can add error reporting service here
          console.error('Production error in project page:', {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            timestamp: new Date().toISOString(),
            url: typeof window !== 'undefined' ? window.location.href : 'unknown'
          });
        }
        
        onError?.(error, errorInfo);
      }}
      onReset={() => {
        // Clear any error state
        if (typeof window !== 'undefined') {
          // Clear any cached error states
          sessionStorage.removeItem('project-error');
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
