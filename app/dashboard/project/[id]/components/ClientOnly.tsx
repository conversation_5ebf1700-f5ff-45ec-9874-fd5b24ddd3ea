"use client";

import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * ClientOnly component to prevent hydration issues in production
 * This ensures the component only renders on the client side after hydration
 */
export function ClientOnly({ children, fallback }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return (
      fallback || (
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-4 text-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">Loading Project</h3>
          <p className="text-sm text-muted-foreground">Initializing client-side components...</p>
        </div>
      )
    );
  }

  return <>{children}</>;
}
