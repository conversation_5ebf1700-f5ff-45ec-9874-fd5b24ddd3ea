"use client"

import { useState, useEffect, useCallback, useRef } from 'react';
import { useParams } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/auth-provider';
import { useProjectStore } from '@/lib/store/project';
import { supabase } from '@/lib/supabase-client';
import { UseFormReturn } from 'react-hook-form';
import { extractProductDetails } from '../../utils/extractProductDetails';


interface IdeaFormData {
  idea: string;
}

interface UseProjectDataProps {
  ideaForm: UseFormReturn<IdeaFormData>;
  initialProject?: any | null;
}

export function useProjectData({ ideaForm, initialProject }: UseProjectDataProps) {
  const params = useParams();
  const { toast } = useToast();
  const { user } = useAuth();
  
  // Zustand store selectors - optimized individual selectors
  const storeProjectId = useProjectStore((state) => state.projectId);
  const projectName = useProjectStore((state) => state.projectName);
  const isTestUser = useProjectStore((state) => state.isTestUser);
  const createdAt = useProjectStore((state) => state.createdAt);
  const updatedAt = useProjectStore((state) => state.updatedAt);
  const idea = useProjectStore((state) => state.idea);
  const productDetails = useProjectStore((state) => state.productDetails);
  
  // Store actions
  const setProjectId = useProjectStore((state) => state.setProjectId);
  const setProjectName = useProjectStore((state) => state.setProjectName);
  const setCreatedAt = useProjectStore((state) => state.setCreatedAt);
  const setUpdatedAt = useProjectStore((state) => state.setUpdatedAt);
  const setIdea = useProjectStore((state) => state.setIdea);
  const setRefinedIdea = useProjectStore((state) => state.setRefinedIdea);
  const setSelectedToolsInStore = useProjectStore((state) => state.setSelectedTools);
  const setProjectPlanInStore = useProjectStore((state) => state.setProjectPlan);
  const setProductDetails = useProjectStore((state) => state.setProductDetails);
  const setVoiceNoteUrlInStore = useProjectStore((state) => state.setVoiceNoteUrl);
  const setIsTestUser = useProjectStore((state) => state.setIsTestUser);
  const updateProjectInStore = useProjectStore((state) => state.updateProject);
  
  // Local state
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [step1Complete, setStep1Complete] = useState(false);
  const [step2Complete, setStep2Complete] = useState(false);
  const [step3Complete, setStep3Complete] = useState(false);
  const [step4Complete, setStep4Complete] = useState(false);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [projectPlan, setProjectPlan] = useState<string>("");
  const [voiceNoteUrl, setVoiceNoteUrl] = useState<string | null>(null);
  const [tempProjectName, setTempProjectName] = useState("");
  const [currentDisplayStep, setCurrentDisplayStep] = useState(1);
  const hydrated = useRef(false);

  // Load project data effect with proper cleanup and race condition handling
  useEffect(() => {
    const projectIdFromParams = params.id as string;
    let isCancelled = false; // Prevent race conditions
    if (!hydrated.current && initialProject && initialProject.id === projectIdFromParams) {
      console.log('Hydrating project store with server data');
      setProjectId(initialProject.id);
      setProjectName(initialProject.name || 'Untitled Project');
      setCreatedAt(initialProject.created_at ? new Date(initialProject.created_at) : new Date());
      setUpdatedAt(initialProject.updated_at ? new Date(initialProject.updated_at) : new Date());
      setIdea(initialProject.idea || '');
      if (initialProject.refined_idea) setRefinedIdea(initialProject.refined_idea);
      if (initialProject.selected_tools) setSelectedToolsInStore(initialProject.selected_tools);
      if (initialProject.project_plan) setProjectPlanInStore(initialProject.project_plan);
      const productDetailsData = extractProductDetails(initialProject);
      setProductDetails({
        targetAudience: '',
        keyFeatures: [],
        frontendTech: [],
        backendTech: [],
        usp: [],
        ...productDetailsData,
      });
      ideaForm.setValue('idea', initialProject.idea || '');
      setSelectedTools(initialProject.selected_tools || []);
      setProjectPlan(initialProject.project_plan || '');
      setVoiceNoteUrl(initialProject.voice_note_url || null);
      setTempProjectName(initialProject.name || 'Untitled Project');
      setStep1Complete(!!initialProject.idea);
      setStep2Complete(!!initialProject.product_details && Object.keys(initialProject.product_details).length > 0);
      setStep3Complete(!!initialProject.selected_tools && initialProject.selected_tools.length > 0);
      setStep4Complete(!!initialProject.project_plan);
      hydrated.current = true;
      setIsLoading(false);
      return;
    }

    console.log('useProjectData effect triggered:', {
      projectIdFromParams,
      storeProjectId,
      isTestUser,
      userExists: !!user
    });

    if (!projectIdFromParams) {
      console.error("No project ID found in URL params.");
      setError("No project ID specified in the URL.");
      setIsLoading(false);
      return;
    }

    // Update store projectId only if it's different
    if (storeProjectId !== projectIdFromParams) {
      console.log(`Updating store projectId from ${storeProjectId} to ${projectIdFromParams}`);
      
      setProjectId(projectIdFromParams);
      
      // Reset local state related to the previous project
      setStep1Complete(false);
      setStep2Complete(false);
      setStep3Complete(false);
      setStep4Complete(false);
      setSelectedTools([]);
      setProjectPlan("");
      setVoiceNoteUrl(null);
      ideaForm.reset({ idea: "" });
      
      console.log("Local state reset for new project");
    }

    const loadProject = async () => {
      if (isCancelled) return; // Early return if component unmounted
      
      setIsLoading(true);
      setError(null);

      if (isTestUser) {
        console.log("Test user detected, skipping data load.");
        if (!isCancelled) {
          setIsLoading(false);
          setTempProjectName("Test Project");
          setProjectName("Test Project");
        }
        return;
      }

      try {
        console.log(`Loading project data for ID: ${projectIdFromParams}`);
        const fetchStart = Date.now();
        // Force fresh data fetch by adding timestamp to prevent caching
        const fetchPromise = supabase
          .from('projects')
          .select('*')
          .eq('id', projectIdFromParams)
          .maybeSingle();

        // Race the Supabase fetch against a 10 second timeout
        let timeoutId: NodeJS.Timeout;
        const timeoutPromise = new Promise<never>((_, reject) => {
          timeoutId = setTimeout(
            () => reject(new Error('Request timed out while loading project.')),
            10000
          );
        });

        const { data, error: dbError } = await Promise.race([
          fetchPromise,
          timeoutPromise,
        ]).finally(() => {
          if (timeoutId) clearTimeout(timeoutId);
        });
        console.log('project fetch took', Date.now() - fetchStart, 'ms');

        if (isCancelled) return; // Check cancellation after async operation

        if (dbError) throw dbError;

        if (data) {
          console.log("Project data loaded:", data);
          
          // Update Store - only if not cancelled
          if (!isCancelled) {
            setProjectName(data.name || "Untitled Project");
            setCreatedAt(data.created_at ? new Date(data.created_at) : new Date());
            setUpdatedAt(data.updated_at ? new Date(data.updated_at) : new Date());
            setIdea(data.idea || '');
            if (data.refined_idea) setRefinedIdea(data.refined_idea);
            if (data.selected_tools) setSelectedToolsInStore(data.selected_tools);
            if (data.project_plan) setProjectPlanInStore(data.project_plan);
            
            // Extract and set product details
            const productDetailsData = extractProductDetails(data);
            const completeProductDetails = {
              targetAudience: "",
              keyFeatures: [],
              frontendTech: [],
              backendTech: [],
              usp: [],
              ...productDetailsData
            };
            setProductDetails(completeProductDetails);
            
            // Update Local State
            ideaForm.setValue('idea', data.idea || '');
            setSelectedTools(data.selected_tools || []);
            setProjectPlan(data.project_plan || '');
            setVoiceNoteUrl(data.voice_note_url || null);
            setTempProjectName(data.name || "Untitled Project");

            // Set Step Completion
            setStep1Complete(!!data.idea);
            setStep2Complete(!!data.product_details && Object.keys(data.product_details).length > 0);
            setStep3Complete(!!data.selected_tools && data.selected_tools.length > 0);
            setStep4Complete(!!data.project_plan);
          }
        } else {
          // Create new project if it doesn't exist
          console.log(`Project with ID ${projectIdFromParams} not found. Creating a new one.`);
          
          const { error: createError } = await supabase
            .from('projects')
            .insert({
              id: projectIdFromParams,
              name: "New Project",
              user_id: user?.id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              status: 'draft'
            });
            
          if (isCancelled) return;
            
          if (createError) {
            console.error("Error creating new project:", createError);
            throw createError;
          }
          
          if (!isCancelled) {
            setProjectName("New Project");
            setTempProjectName("New Project");
            setCreatedAt(new Date());
            setUpdatedAt(new Date());
          }
        }
      } catch (err) {
        if (!isCancelled) {
          console.error("Error loading project:", err);
          setError(err instanceof Error ? err.message : "Failed to load project");
        }
      } finally {
        if (!isCancelled) {
          setIsLoading(false);
        }
      }
    };

    // Always load project data when component mounts or projectId changes
    if (projectIdFromParams) {
      console.log(`Loading project data for: ${projectIdFromParams}`);
      loadProject();
    }

    // Cleanup function to prevent race conditions
    return () => {
      isCancelled = true;
    };
  }, [
    params.id,
    user?.id,
  ]);

  // Check for test user
  useEffect(() => {
    if (user && user.email === "<EMAIL>") {
      setIsTestUser(true);
      console.log("Test user detected.");
    }
  }, [user, setIsTestUser]);

  // Sync tempProjectName when projectName from store changes
  useEffect(() => {
    setTempProjectName(projectName);
  }, [projectName]);

  // Track step completion based on idea form
  useEffect(() => {
    if (ideaForm.getValues("idea") && ideaForm.getValues("idea").trim() !== "") {
      setStep1Complete(true);
    }
  }, [ideaForm]);

  // Determine current step for rendering
  useEffect(() => {
    let newStep = 1;
    if (step4Complete) newStep = 5;
    else if (step3Complete) newStep = 4;
    else if (step2Complete) newStep = 3;
    else if (step1Complete) newStep = 2;
    
    if (currentDisplayStep !== newStep) {
      console.log(`Auto-updating current step from ${currentDisplayStep} to ${newStep}`);
      setCurrentDisplayStep(newStep);
    }
  }, [step1Complete, step2Complete, step3Complete, step4Complete, currentDisplayStep]);

  // Handle page visibility changes (tab switching back after long time)
  // DISABLED: Auto-refresh was causing unwanted reloading behavior
  // The page should keep content cached instead of triggering rerenders
  // useEffect(() => {
  //   const handleVisibilityChange = () => {
  //     if (!document.hidden && storeProjectId) {
  //       // Tab became visible again - refresh data if it's been more than 5 minutes
  //       const lastRefresh = sessionStorage.getItem(`project-${storeProjectId}-last-refresh`);
  //       const now = Date.now();
  //       const fiveMinutes = 5 * 60 * 1000;
  //       
  //       if (!lastRefresh || (now - parseInt(lastRefresh)) > fiveMinutes) {
  //         console.log('Tab became visible after long time, refreshing project data...');
  //         // Trigger a soft refresh by updating the project ID to force re-load
  //         const currentId = storeProjectId;
  //         setProjectId(''); // Clear temporarily
  //         setTimeout(() => setProjectId(currentId), 10); // Re-set to trigger reload
  //         sessionStorage.setItem(`project-${storeProjectId}-last-refresh`, now.toString());
  //       }
  //     }
  //   };

  //   document.addEventListener('visibilitychange', handleVisibilityChange);
    
  //   // Set initial timestamp
  //   if (storeProjectId) {
  //     sessionStorage.setItem(`project-${storeProjectId}-last-refresh`, Date.now().toString());
  //   }

  //   return () => {
  //     document.removeEventListener('visibilitychange', handleVisibilityChange);
  //   };
  // }, [storeProjectId, setProjectId]);

  // Handle project name update
  const handleProjectNameUpdate = useCallback(async () => {
    if (!tempProjectName.trim()) {
      toast({ title: "Project name cannot be empty", variant: "destructive" });
      return;
    }
    
    const originalName = projectName;
    try {
      setProjectName(tempProjectName);
      
      if (!isTestUser && storeProjectId) {
        const { error } = await supabase
          .from('projects')
          .update({ 
            name: tempProjectName,
            updated_at: new Date().toISOString()
          })
          .eq('id', storeProjectId);
          
        if (error) throw error;
      }
      
      toast({ title: "Project name updated successfully" });
    } catch (error: any) {
      console.error("Error updating project name:", error);
      setProjectName(originalName);
      setTempProjectName(originalName);
      toast({
        title: "Failed to update project name",
        description: error.message,
        variant: "destructive"
      });
    }
  }, [tempProjectName, projectName, isTestUser, storeProjectId, setProjectName, toast]);

  // Step completion handler
  const handleStepComplete = useCallback(async (stepNumber: number) => {
    try {
      if (stepNumber === 1) {
        const ideaValue = ideaForm.getValues("idea");
        if (!ideaValue || ideaValue.length < 10) {
          toast({ title: "Idea must be at least 10 characters", variant: "destructive" });
          return false;
        }
        setStep1Complete(true);
      } else if (stepNumber === 2) {
        setStep2Complete(true);
      } else if (stepNumber === 3) {
        setStep3Complete(true);
      } else if (stepNumber === 4) {
        if (!projectPlan || projectPlan.trim() === '') {
          toast({ title: "Please generate a project plan first", variant: "destructive" });
          return false;
        }
        setStep4Complete(true);
      }

      // Update the store with the current state
      if (storeProjectId && !isTestUser) {
        let updateData: any = {};

        if (stepNumber === 1) {
          updateData = { idea: ideaForm.getValues("idea") };
          setIdea(ideaForm.getValues("idea"));
        } else if (stepNumber === 3) {
          updateData = { selected_tools: selectedTools };
          setSelectedToolsInStore(selectedTools);
        } else if (stepNumber === 4) {
          updateData = { project_plan: projectPlan };
          setProjectPlanInStore(projectPlan);
        }

        if (Object.keys(updateData).length > 0) {
          console.log(`Updating project for step ${stepNumber} with:`, updateData);

          // Update database directly
          const { error } = await supabase
            .from('projects')
            .update({
              ...updateData,
              updated_at: new Date().toISOString()
            })
            .eq('id', storeProjectId);

          if (error) throw error;
        }
      }
      
      console.log(`Step ${stepNumber} completed.`);
      return true;

    } catch (error: any) {
      console.error(`Error completing step ${stepNumber}:`, error);
      toast({
        title: `Failed to save progress for Step ${stepNumber}`,
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  }, [ideaForm, projectPlan, selectedTools, storeProjectId, updateProjectInStore, toast]);

  return {
    // State
    isLoading,
    error,
    step1Complete,
    step2Complete,
    step3Complete,
    step4Complete,
    selectedTools,
    projectPlan,
    voiceNoteUrl,
    tempProjectName,
    currentDisplayStep,
    
    // Store values
    storeProjectId,
    projectName,
    isTestUser,
    createdAt,
    updatedAt,
    idea,
    productDetails,
    
    // Setters
    setStep1Complete,
    setStep2Complete,
    setStep3Complete,
    setStep4Complete,
    setSelectedTools,
    setProjectPlan,
    setVoiceNoteUrl,
    setTempProjectName,
    setCurrentDisplayStep,
    
    // Handlers
    handleProjectNameUpdate,
    handleStepComplete,
  };
}