/**
 * Production-specific optimizations to prevent loading issues
 */

/**
 * Debounce function to prevent rapid re-renders
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Check if we're in production environment
 */
export const isProduction = process.env.NODE_ENV === 'production';

/**
 * Check if we're running on Vercel
 */
export const isVercel = process.env.VERCEL === '1';

/**
 * Get production-safe timeout values
 */
export const getTimeouts = () => ({
  auth: isProduction ? 10000 : 5000, // 10s in prod, 5s in dev
  data: isProduction ? 15000 : 10000, // 15s in prod, 10s in dev
  retry: isProduction ? 2000 : 1000, // 2s in prod, 1s in dev
});

/**
 * Production-safe error handler
 */
export function handleProductionError(error: Error, context: string) {
  console.error(`[${context}] Error:`, error);
  
  if (isProduction) {
    // In production, log minimal error info
    console.error('Production error:', {
      message: error.message,
      context,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'unknown'
    });
  } else {
    // In development, log full error
    console.error('Development error:', error);
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries - 1) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Check if the current environment has hydration issues
 */
export function hasHydrationIssues(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Check for common hydration issue indicators
  const userAgent = window.navigator.userAgent;
  const isBot = /bot|crawler|spider|crawling/i.test(userAgent);
  
  return isBot || isVercel;
}

/**
 * Safe localStorage wrapper that handles SSR
 */
export const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  },
  
  setItem: (key: string, value: string): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      localStorage.setItem(key, value);
      return true;
    } catch {
      return false;
    }
  },
  
  removeItem: (key: string): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      localStorage.removeItem(key);
      return true;
    } catch {
      return false;
    }
  }
};

/**
 * Safe sessionStorage wrapper that handles SSR
 */
export const safeSessionStorage = {
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return sessionStorage.getItem(key);
    } catch {
      return null;
    }
  },
  
  setItem: (key: string, value: string): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      sessionStorage.setItem(key, value);
      return true;
    } catch {
      return false;
    }
  },
  
  removeItem: (key: string): boolean => {
    if (typeof window === 'undefined') return false;
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch {
      return false;
    }
  }
};
