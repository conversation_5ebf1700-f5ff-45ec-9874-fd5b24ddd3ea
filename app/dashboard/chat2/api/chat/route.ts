import { convertToCoreMessages, streamText } from "ai";
import { createOpenRouter } from '@openrouter/ai-sdk-provider';

// Define the system prompt for the chat assistant
const systemPrompt = `You are Provibe AI, an expert Product Management assistant.

Your role is to help users with their product management tasks, including ideation, planning, and documentation.
Use the provided context for the user's project and documents if available. If no specific project context is provided, answer generally.

You can help with:
1. Refining product ideas
2. Creating product documentation
3. Suggesting features and improvements
4. Answering questions about product management
5. Providing guidance on best practices

IMPORTANT: Do NOT respond to GitHub repository URLs. The system has a separate handler for GitHub URLs that will automatically detect them and offer document generation options. Simply ignore any GitHub URLs in the conversation and focus on answering other questions.

Be concise, helpful, and focus on providing actionable advice.`;

// Create OpenRouter client
const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY || '',
  headers: {
    'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://provibe.dev',
    'X-Title': 'Provibe'
  }
});

// Define the model to use with OpenRouter
const aiModel = openrouter("openai/gpt-4o-mini");

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    console.log("[Chat2 API] Received request");

    // Check if OpenRouter API key is available
    if (!process.env.OPENROUTER_API_KEY) {
      console.error("[Chat2 API] Missing OPENROUTER_API_KEY environment variable");
      return new Response(
        JSON.stringify({ error: "API configuration error" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    const { messages } = await req.json();
    console.log("[Chat2 API] Processing", messages?.length || 0, "messages");

    if (!messages || !Array.isArray(messages)) {
      console.error("[Chat2 API] Invalid messages format");
      return new Response(
        JSON.stringify({ error: "Invalid messages format" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Create a stream using streamText with the proper model instance
    const result = streamText({
      model: aiModel,
      messages: [
        { role: "system", content: systemPrompt },
        ...convertToCoreMessages(messages),
      ],
      temperature: 0.7,
      maxTokens: 4000,
      // Add streaming optimizations for smoother output
      frequencyPenalty: 0,
      presencePenalty: 0,
    });

    console.log("[Chat2 API] Streaming response initiated");
    return result.toDataStreamResponse();

  } catch (error) {
    console.error("[Chat2 API] Error in chat route:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to process your request",
        details: error instanceof Error ? error.message : "Unknown error"
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
