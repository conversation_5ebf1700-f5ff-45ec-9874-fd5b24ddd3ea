"use client"

import type React from "react"
import { create<PERSON>ontex<PERSON>, use<PERSON>ontex<PERSON>, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase-client"
import type { User as SupabaseUser } from "@supabase/supabase-js"
import type { Database } from "@/lib/database.types"
import { setSupabaseCookie, hasSupabase<PERSON>ookie, clearSupabaseCookie } from "@/lib/cookie-utils";

type Profile = Database["public"]["Tables"]["profiles"]["Row"]

type User = {
  
  id: string
  name: string
  email: string
  subscription: "free" | "pro"
  avatar?: string
  credits_remaining: number
  projects_limit: number
}

// Define a function to report errors
const reportError = (errorType: string, error: Error) => {
  console.error(`Error of type ${errorType}:`, error);
};

type AuthContextType = {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  loginWithSSO: (provider: "google" | "github") => Promise<void>
  logout: () => Promise<void>
  register: (email: string, password: string, name: string) => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Check if we're in the v0 preview environment
const isV0Preview = typeof window !== "undefined" && window.location.hostname.includes("vusercontent.net")

// Test user for development and testing
const TEST_USER: User = {
  id: "test_user_id",
  name: "Test User",
  email: "<EMAIL>",
  subscription: "pro",
  avatar: `https://ui-avatars.com/api/?name=Test+User&background=10B981&color=fff`,
  credits_remaining: 750,
  projects_limit: 5,
}

// Add this array of public routes that don't require authentication
const PUBLIC_ROUTES = ['/', '/auth/login', '/auth/register', '/auth/callback', '/auth/update-password'];

// Add this function to check if the path should be protected
const isProtectedRoute = (path: string) => {
  return path.startsWith('/dashboard') && 
         !path.startsWith('/terms') && 
         !path.startsWith('/privacy');
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()
  const { toast } = useToast()

  // Convert Supabase user and profile to our User type
  const formatUser = (supabaseUser: SupabaseUser, profile: Profile | null): User => {
    return {
      id: supabaseUser.id,
      name: profile?.full_name || supabaseUser.user_metadata?.full_name || supabaseUser.email?.split("@")[0] || "User",
      email: supabaseUser.email || "",
      subscription: profile?.subscription_tier || "free",
      avatar:
        profile?.avatar_url ||
        supabaseUser.user_metadata?.avatar_url ||
        `https://ui-avatars.com/api/?name=${encodeURIComponent(profile?.full_name || "User")}&background=10B981&color=fff`,
      credits_remaining: profile?.credits_remaining || 1000,
      projects_limit: profile?.subscription_tier === "pro" ? 20 : 2,
    }
  }

  // Check if user is logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user has explicitly logged out
        const userLoggedOut = localStorage.getItem("v0_user_logged_out")

        // In v0 preview, respect the logged out state
        if (isV0Preview) {
          if (userLoggedOut === "true") {
            console.log("User previously logged out, keeping logged out state")
            setUser(null)
            setLoading(false)
            return
          }

          // Only auto-login if not previously logged out
          console.log("Using test user in v0 preview environment")
          setUser(TEST_USER)
          localStorage.setItem("v0_test_user_logged_in", "true")
          setLoading(false)
          return
        }

        // First check for test user in localStorage
        const storedUser = localStorage.getItem("provibe_user")
        if (storedUser && !userLoggedOut) {
          const parsedUser = JSON.parse(storedUser)
          if (parsedUser.email === "<EMAIL>") {
            console.log("Found test user in localStorage")
            setUser(TEST_USER)
            setLoading(false)
            return
          }
        }

        // Then check for an active Supabase session
        console.log("Checking for Supabase session")
        const sessionStart = Date.now()
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession()
        console.log('getSession took', Date.now() - sessionStart, 'ms')

        if (session?.user) {
          console.log("Found active session for user:", session.user.email)
          
          // Ensure the cookie is set
          if (!hasSupabaseCookie()) {
            console.log("Setting Supabase cookie manually");
            setSupabaseCookie(session);
          }
          
          // Set user immediately with session data to avoid loading delay
          const basicUser = formatUser(session.user, null);
          setUser(basicUser);
          setLoading(false);
          
          // Fetch profile in background (non-blocking) with debounce for OAuth
          const fetchProfileAsync = async () => {
            try {
              // Get user profile from Supabase
              const { data: profile, error: profileError } = await supabase
                .from("profiles")
                .select("id, full_name, avatar_url, subscription_tier, credits_remaining")
                .eq("id", session.user.id)
                .single()

              if (profileError && profileError.code !== "PGRST116") {
                console.error("Error fetching profile:", profileError)
                return; // Keep basic user data
              }

              // Create profile if it doesn't exist
              if (!profile) {
                console.log("Creating new profile for user:", session.user.email)
                const newProfile = {
                  id: session.user.id,
                  full_name: session.user.user_metadata?.full_name || session.user.email?.split("@")[0] || "User",
                  avatar_url: session.user.user_metadata?.avatar_url,
                  subscription_tier: "free",
                  credits_remaining: 1000,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                }

                const { error: insertError } = await supabase.from("profiles").insert([newProfile])
                
                if (insertError) {
                  console.error("Error creating profile:", insertError)
                  // Keep basic user data
                  reportError("profile_creation_failed", insertError)
                  return
                }

                // Fetch the newly created profile
                const { data: createdProfile } = await supabase
                  .from("profiles")
                  .select("id, full_name, avatar_url, subscription_tier, credits_remaining")
                  .eq("id", session.user.id)
                  .single()

                if (createdProfile) {
                  const formattedUser = formatUser(session.user, createdProfile)
                  setUser(formattedUser)
                }
              } else {
                const formattedUser = formatUser(session.user, profile)
                setUser(formattedUser)
              }
            } catch (profileError) {
              console.error("Profile error:", profileError)
              // Fallback to basic user info if profile fetch fails
              // Already set basic user above, so no need to set again
            }
          };
          
          // Start background profile fetch
          fetchProfileAsync();
          
        } else if (error) {
          console.error("Supabase auth error:", error)
          
          // Check if token needs refresh
          if (error.message?.includes("token") || error.status === 401) {
            await supabase.auth.refreshSession()
            // Retry auth check after refresh attempt
            const refreshStart = Date.now()
            const { data: refreshData } = await supabase.auth.getSession()
            console.log('refresh getSession took', Date.now() - refreshStart, 'ms')
            if (refreshData.session) {
              // Session refreshed successfully, restart auth check
              checkAuth()
              return
            }
          }
          
          // If we're not on an auth page, redirect to login
          if (!pathname?.includes("/auth/") && !PUBLIC_ROUTES.includes(pathname || '')) {
            router.push("/auth/login")
          }
        } else if (!pathname?.includes("/auth/") && !PUBLIC_ROUTES.includes(pathname || '') && isProtectedRoute(pathname || '')) {
          // Only redirect to login if on a protected route
          console.log("No active session, redirecting to login")
          router.push("/auth/login")
        }
      } catch (error) {
        console.error("Authentication error:", error)
        // If there's an error and we're not on an auth page, redirect to login
        if (!pathname?.includes("/auth/") && isProtectedRoute(pathname || '')) {
          router.push("/auth/login")
        }
      } finally {
        setLoading(false)
      }
    }

    // Set up auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("🔵 Auth provider: Auth state changed:", event);
      
      // AGGRESSIVELY skip during callback redirect
      if (typeof window !== 'undefined') {
        const isCallback = sessionStorage.getItem('auth_callback_active');
        const isCallbackPage = window.location.pathname === '/auth/callback';
        
        if (isCallback || isCallbackPage) {
          console.log("🛑 Auth provider: Callback mode - COMPLETELY SKIPPING auth state handling");
          return;
        }
      }
      
      if (session && (event === "SIGNED_IN" || event === "TOKEN_REFRESHED")) {
        // Ensure the cookie is set
        if (!hasSupabaseCookie()) {
          console.log("Auth provider: Setting Supabase cookie on auth state change");
          setSupabaseCookie(session);
        }
        
        // For OAuth callback performance, set basic user first
        const basicUser = formatUser(session.user, null);
        setUser(basicUser);
        
        // Skip heavy profile fetch if in OAuth callback mode
        const isOAuthCallback = typeof window !== 'undefined' && 
          (window.location.pathname === '/auth/callback' || 
           sessionStorage.getItem('auth_callback_active') === 'true');
        
        if (!isOAuthCallback) {
          try {
            // Get user profile
            const { data: profile } = await supabase
              .from("profiles")
              .select("id, full_name, avatar_url, subscription_tier, credits_remaining")
              .eq("id", session.user.id)
              .single();
            
            // Format user with profile data
            const formattedUser = formatUser(session.user, profile);
            setUser(formattedUser);
          } catch (error) {
            console.error("Error updating user on auth state change:", error);
            // Keep basic user data
          }
        }
      } else if (event === "SIGNED_OUT") {
        setUser(null);
        clearSupabaseCookie();
      }
    });

    // Prevent race conditions by setting a flag
    let isMounted = true
    
    // Only run checkAuth if component is still mounted
    const safeCheckAuth = async () => {
      if (isMounted) {
        await checkAuth()
      }
    }
    
    safeCheckAuth()

    return () => {
      isMounted = false
      if (!isV0Preview) {
        authListener.subscription.unsubscribe()
      }
    }
  }, [pathname, router])

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      // Clear the logged out flag when user explicitly logs in
      localStorage.removeItem("v0_user_logged_out");

      // In v0 preview, use test user
      if (isV0Preview) {
        console.log("Using test user in v0 preview environment");
        setUser(TEST_USER);
        localStorage.setItem("v0_test_user_logged_in", "true");
        return TEST_USER;
      }

      // Special case for test user
      if (email === "<EMAIL>" && password === "password123") {
        console.log("Logging in as test user");
        localStorage.setItem("provibe_user", JSON.stringify(TEST_USER));
        setUser(TEST_USER);
        return TEST_USER;
      }

      // Regular Supabase authentication
      console.log("Attempting Supabase login with:", email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("Supabase login error:", error);
        throw error;
      }

      if (!data.user) {
        throw new Error("No user returned from login");
      }

      console.log("Login successful for:", data.user?.email);

      // Manually set the cookie if it's not already set
      if (data.session && !hasSupabaseCookie()) {
        console.log("Setting Supabase cookie manually");
        setSupabaseCookie(data.session);
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("profiles")
        .select("id, full_name, avatar_url, subscription_tier, credits_remaining")
        .eq("id", data.user.id)
        .single();
      
      // Format and set user
      const formattedUser = formatUser(data.user, profile);
      
      // Update state and localStorage atomically
      setUser(formattedUser);
      localStorage.setItem("provibe_user", JSON.stringify(formattedUser));
      
      console.log("User state updated:", formattedUser);
      return formattedUser;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  }

  const loginWithSSO = async (provider: "google" | "github") => {
    setLoading(true)
    try {
      // Clear the logged out flag when user explicitly logs in
      localStorage.removeItem("v0_user_logged_out")

      // In v0 preview, use test user
      if (isV0Preview) {
        console.log("Using test user in v0 preview environment")
        setUser(TEST_USER)
        localStorage.setItem("v0_test_user_logged_in", "true")
        setLoading(false)
        return
      }

      console.log(`Initiating ${provider} SSO login`)
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          scopes: provider === 'github' ? 'user:email' : 'email profile',
        },
      })

      if (error) {
        console.error(`${provider} SSO error:`, error)
        throw error
      }

      console.log(`${provider} SSO initiated:`, data)
      // The actual user data will be set after the OAuth redirect
    } catch (error) {
      console.error(`${provider} login error:`, error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    setLoading(true);
    try {
      // Clear local state immediately
      setUser(null);

      // Remove all auth-related items from localStorage
      localStorage.removeItem("provibe_user");
      localStorage.removeItem("supabase.auth.token");
      localStorage.removeItem("v0_test_user_logged_in");
      
      // Clear the Supabase cookie
      clearSupabaseCookie();

      // For v0 preview environment, set a flag to prevent auto-login
      if (isV0Preview) {
        console.log("V0 preview environment detected, setting logged out state");
        localStorage.setItem("v0_user_logged_out", "true");
        router.push("/auth/login");
        return;
      }

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error("Supabase signOut error:", error);
        // Even if there's an error, force redirect to login
        router.push("/auth/login");
        return;
      }
      
      // Redirect to login page
      router.push("/auth/login");
    } catch (error) {
      console.error("Logout error:", error);
      // Force redirect to login even if there's an error
      router.push("/auth/login");
    } finally {
      setLoading(false);
    }
  }

  const register = async (email: string, password: string, name: string) => {
    setLoading(true)
    try {
      // Clear the logged out flag when user explicitly registers
      localStorage.removeItem("v0_user_logged_out")

      // In v0 preview, use test user
      if (isV0Preview) {
        console.log("Using test user in v0 preview environment")
        setUser(TEST_USER)
        localStorage.setItem("v0_test_user_logged_in", "true")
        return
      }

      console.log("Registering new user:", email)

      // First, sign up the user with Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: name,
          },
        },
      })

      if (error) {
        console.error("Registration error:", error)
        throw error
      }

      if (!data.user) {
        throw new Error("User registration failed")
      }

      console.log("Registration successful for:", data.user?.email)

      // Create profile for the new user
      console.log("Creating profile for new user with ID:", data.user.id)
      const newProfile = {
        id: data.user.id,
        full_name: name,
        avatar_url: null,
        subscription_tier: "free",
        credits_remaining: 1000,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      const { error: profileError } = await supabase.from("profiles").insert([newProfile])

      if (profileError) {
        console.error("Error creating profile:", profileError)
        // Don't throw here, as the user is already created
      }

      const formattedUser = formatUser(data.user, newProfile as Profile)
      setUser(formattedUser)
    } catch (error) {
      console.error("Registration error:", error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    setLoading(true);
    try {
      console.log("Refreshing user data");
      
      // Get current session
      const refreshStart = Date.now();
      const { data: { session }, error } = await supabase.auth.getSession();
      console.log('refreshUser getSession took', Date.now() - refreshStart, 'ms');
      
      if (error) {
        console.error("Error refreshing session:", error);
        throw error;
      }
      
      if (!session) {
        console.log("No active session found during refresh");
        setUser(null);
        return null;
      }
      
      // Ensure the cookie is set
      if (!hasSupabaseCookie()) {
        console.log("Setting Supabase cookie during refresh");
        setSupabaseCookie(session);
      }
      
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id, full_name, avatar_url, subscription_tier, credits_remaining")
        .eq("id", session.user.id)
        .single();
        
      if (profileError && profileError.code !== "PGRST116") {
        console.error("Error fetching profile during refresh:", profileError);
      }
      
      // Format user with updated profile data
      const formattedUser = formatUser(session.user, profile);
      setUser(formattedUser);
      
      console.log("User data refreshed successfully");
      return formattedUser;
    } catch (error) {
      console.error("Error refreshing user:", error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, loginWithSSO, logout, register, refreshUser }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
