import { createClient } from "@supabase/supabase-js"
import type { Database } from "@/types/supabase"

// Use let instead of const to allow conditional assignment
let supabaseInstance: ReturnType<typeof createClient<Database>> | null = null

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables")
}

// Simplified cookie configuration relying on NEXT_PUBLIC_APP_DOMAIN
const getCookieConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const appDomain = process.env.NEXT_PUBLIC_APP_DOMAIN

  if (appDomain) {
    console.log('Using explicit domain from NEXT_PUBLIC_APP_DOMAIN:', appDomain)
  }

  return {
    name: appDomain ? 'sb-provibe-auth-token' : 'sb-provibe-dev-auth-token',
    lifetime: 60 * 60 * 8,
    domain: appDomain || '',
    path: '/',
    sameSite: 'lax' as const,
    secure: isProduction,
  }
}

// Create a singleton instance of the Supabase client
export const supabase = (() => {
  if (supabaseInstance) return supabaseInstance

  const cookieConfig = getCookieConfig()

  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: "pkce", // Use PKCE flow for better security
      storageKey: `supabase.auth.token.${cookieConfig.name}`, // Use domain-specific storage key
    },
  })

  return supabaseInstance
})()

// Export a function to get the client for cases where you need a fresh reference
export const getSupabaseClient = () => supabase

// Export the cookie configuration for use in other parts of the app
export const getSupabaseCookieConfig = getCookieConfig

// Export a function to check if we're in a specific deployment environment
export const getDeploymentInfo = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const cookieConfig = getCookieConfig()

  // Determine current domain using the same logic as getCookieConfig
  let currentDomain = ''
  if (process.env.NEXT_PUBLIC_APP_DOMAIN) {
    currentDomain = process.env.NEXT_PUBLIC_APP_DOMAIN
  } else if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    currentDomain = process.env.NEXT_PUBLIC_VERCEL_URL
  } else if (process.env.VERCEL_URL) {
    currentDomain = process.env.VERCEL_URL
  } else if (typeof window !== 'undefined') {
    currentDomain = window.location.hostname
  }

  return {
    isProduction,
    currentDomain,
    detectionMethod: process.env.NEXT_PUBLIC_APP_DOMAIN ? 'explicit' :
                    process.env.NEXT_PUBLIC_VERCEL_URL ? 'vercel_public' :
                    process.env.VERCEL_URL ? 'vercel_internal' :
                    typeof window !== 'undefined' ? 'runtime' : 'unknown',
    isMainApp: currentDomain === 'www.provibe.dev' ||
               currentDomain === 'provibe.dev' ||
               currentDomain === 'www.provibe.io' ||
               currentDomain === 'provibe.io',
    isAppDomain: currentDomain.includes('app.provibe.io'),
    isLocalDev: currentDomain.includes('localhost') || currentDomain.includes('127.0.0.1'),
    cookieConfig
  }
}
