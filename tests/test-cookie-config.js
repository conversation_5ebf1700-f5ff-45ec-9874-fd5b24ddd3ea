#!/usr/bin/env node

/**
 * Test script to verify cookie configuration for different domains
 * Run with: node scripts/test-cookie-config.js
 */

// Mock environment variables for testing
const testConfigs = [
  {
    name: 'Local Development (Explicit)',
    env: {
      NODE_ENV: 'development',
      NEXT_PUBLIC_APP_DOMAIN: 'localhost:3000'
    }
  },
  {
    name: 'Main App Production (Explicit)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_DOMAIN: 'www.provibe.dev'
    }
  },
  {
    name: 'App Domain Production (Explicit)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_DOMAIN: 'app.provibe.io'
    }
  },
  {
    name: 'Vercel Auto-Detection (Public URL)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_VERCEL_URL: 'staging.provibe.io'
    }
  },
  {
    name: 'Vercel Auto-Detection (Internal URL)',
    env: {
      NODE_ENV: 'production',
      VERCEL_URL: 'preview-abc123.vercel.app'
    }
  },
  {
    name: 'Custom Domain (Fallback)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_DOMAIN: 'my-custom-domain.com'
    }
  },
  {
    name: 'No Configuration (Runtime Detection)',
    env: {
      NODE_ENV: 'production'
      // No domain configuration - would use runtime detection
    }
  }
];

// Mock the getCookieConfig function with new domain detection logic
function getCookieConfig(env) {
  const isProduction = env.NODE_ENV === 'production';

  const appDomain = env.NEXT_PUBLIC_APP_DOMAIN || '';

  const cookieName = appDomain ? 'sb-provibe-auth-token' : 'sb-provibe-dev-auth-token';
  const domain = appDomain;

  return {
    name: cookieName,
    lifetime: 60 * 60 * 8,
    domain,
    path: '/',
    sameSite: 'lax',
    secure: isProduction,
  };
}

console.log('🍪 Cookie Configuration Test\n');
console.log('Testing cookie configurations for different deployment environments:\n');

testConfigs.forEach((config, index) => {
  console.log(`${index + 1}. ${config.name}`);
  console.log(`   Environment: ${JSON.stringify(config.env, null, 2)}`);

  const cookieConfig = getCookieConfig(config.env);

  console.log(`   Cookie Config:`);
  console.log(`     Cookie Name: ${cookieConfig.name}`);
  console.log(`     Cookie Domain: ${cookieConfig.domain || '(current domain)'}`);
  console.log(`     Path: ${cookieConfig.path}`);
  console.log(`     Secure: ${cookieConfig.secure}`);
  console.log(`     SameSite: ${cookieConfig.sameSite}`);
  console.log(`     Lifetime: ${cookieConfig.lifetime}s (${cookieConfig.lifetime / 3600}h)`);
  console.log('');
});

console.log('✅ All configurations generated successfully!');
console.log('\n📝 Key Benefits for Same Repository Deployment:');
console.log('- ✅ Same codebase works on multiple domains');
console.log('- ✅ Simple configuration with NEXT_PUBLIC_APP_DOMAIN');
console.log('- ✅ Production-ready security settings');
console.log('\n🚀 Deploy the same /app directory to unlimited domains!');
